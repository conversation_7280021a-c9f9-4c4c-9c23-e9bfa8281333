/**
 * S3 Upload Types and Interfaces
 * Defines TypeScript interfaces for S3 pre-signed URL uploads
 */

// API Request/Response Types
export interface S3PreSignedUrlRequest {
  filename: string;
}

export interface S3PreSignedUrlResponse {
  statusCode: number;
  body: string; // JSON string that needs to be parsed
}

export interface S3PreSignedUrlBody {
  uploadUrl: string;
  viewUrl: string;
  objectKey: string;
}

// Image Upload Configuration
export interface ImageUploadConfig {
  maxSizeBytes: number;
  maxWidth: number;
  maxHeight: number;
  minWidth: number;
  minHeight: number;
  allowedTypes: string[];
  allowedExtensions: string[];
}

// Audio Upload Configuration
export interface AudioUploadConfig {
  maxSizeBytes: number;
  allowedTypes: string[];
  allowedExtensions: string[];
}

// Image Validation Result
export interface ImageValidationResult {
  isValid: boolean;
  error?: string;
  warnings?: string[];
}

// Audio Validation Result
export interface AudioValidationResult {
  isValid: boolean;
  error?: string;
  warnings?: string[];
}

// Upload State Management
export interface ImageUploadState {
  isUploading: boolean;
  progress: number;
  error: string | null;
  uploadedUrl: string | null;
  previewUrl: string | null;
}

export interface AudioUploadState {
  isUploading: boolean;
  progress: number;
  error: string | null;
  uploadedUrl: string | null;
  previewUrl: string | null;
}

// Upload Result
export interface ImageUploadResult {
  success: boolean;
  url?: string;
  error?: string;
  objectKey?: string;
}

export interface AudioUploadResult {
  success: boolean;
  url?: string;
  error?: string;
  objectKey?: string;
}

// Upload Progress Callback
export type UploadProgressCallback = (progress: number) => void;

// Upload Options
export interface ImageUploadOptions {
  onProgress?: UploadProgressCallback;
}

export interface AudioUploadOptions {
  onProgress?: UploadProgressCallback;
}

// Default Configuration
export const DEFAULT_IMAGE_UPLOAD_CONFIG: ImageUploadConfig = {
  maxSizeBytes: 5 * 1024 * 1024, // 5MB
  maxWidth: 2048,
  maxHeight: 2048,
  minWidth: 100,
  minHeight: 100,
  allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  allowedExtensions: ['.jpg', '.jpeg', '.png', '.webp'],
};

export const DEFAULT_AUDIO_UPLOAD_CONFIG: AudioUploadConfig = {
  maxSizeBytes: 100 * 1024 * 1024, // 100MB
  allowedTypes: ['audio/wav', 'audio/mpeg', 'audio/mp3', 'audio/aiff', 'audio/x-aiff', 'audio/mp4', 'audio/aac'],
  allowedExtensions: ['.aif', '.aiff', '.wav', '.mp3', '.mp4', '.aac'],
};

// API Configuration
export const S3_UPLOAD_API_CONFIG = {
  endpoint: 'https://57k50sc89a.execute-api.us-east-2.amazonaws.com/staging/generate-signed-url',
  filePathPrefix: 'users/example_name/',
} as const;
